<script>
	export default {
		onLaunch: function() {
			console.log('App Launch')
		},
		onShow: function() {
			console.log('App Show')
		},
		onHide: function() {
			console.log('App Hide')
		}
	}
</script>

<style>
	/* 每个页面公共css - 企业微信风格 */
	page {
		background: #F7F7F7; /* 企业微信背景色 */
		font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', SimSun, sans-serif;
		color: #191F25; /* 主要文字色 */
		font-size: 28upx;
		line-height: 1.6;
	}

	.custom-nav {
		padding-top: 200upx;
	}

	.esplict-txt {
		/* 添加文字溢出显示省略号 */
		width: 100%;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	/* 企业微信风格通用样式 */
	.wework-card {
		background: #FFFFFF;
		border-radius: 16upx;
		box-shadow: 0 2upx 12upx rgba(0, 0, 0, 0.04);
		margin: 24upx 32upx;
		overflow: hidden;
	}

	.wework-title {
		font-size: 32upx;
		font-weight: 500;
		color: #191F25;
		line-height: 1.4;
	}

	.wework-subtitle {
		font-size: 28upx;
		font-weight: 400;
		color: #576B95;
		line-height: 1.4;
	}

	.wework-text {
		font-size: 28upx;
		font-weight: 400;
		color: #191F25;
		line-height: 1.6;
	}

	.wework-text-secondary {
		font-size: 24upx;
		font-weight: 400;
		color: #B2B2B2;
		line-height: 1.5;
	}

	.wework-btn-primary {
		background: #576B95;
		color: #FFFFFF;
		border-radius: 8upx;
		font-size: 28upx;
		font-weight: 500;
		border: none;
		padding: 24upx 32upx;
	}

	.wework-btn-secondary {
		background: #FFFFFF;
		color: #576B95;
		border: 2upx solid #576B95;
		border-radius: 8upx;
		font-size: 28upx;
		font-weight: 500;
		padding: 24upx 32upx;
	}

	.wework-divider {
		height: 2upx;
		background: #E5E5EA;
		margin: 0 32upx;
	}
</style>