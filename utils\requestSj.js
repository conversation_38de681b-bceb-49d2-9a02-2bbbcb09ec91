/**
 * 通用请求封装
 * 支持请求拦截、响应拦截、错误统一处理
 */
let config  = require('./' +process.env.NODE_ENV)
console.log("sjbaseApi", config)
// 基础配置
const BASE_URL = config.sjbaseApi; // 替换为你的实际接口地址
const TIMEOUT = 60000; // 请求超时时间，单位ms
// 请求拦截器
const requestInterceptor = (config) => {
  // 获取token
  const token = uni.getStorageSync('token');
  
  // 添加通用header
  if (!config.header) {
    config.header = {};
  }
  
  // 设置token
  if (token) {
    config.header['Authorization'] = `Bearer ${token}`;
  }
  
  // 设置内容类型
  if (!config.header['Content-Type']) {
    config.header['Content-Type'] = 'application/json';
  }
  
  // 添加时间戳防止缓存
  if (config.url.indexOf('?') === -1) {
    config.url = config.url + '?_t=' + new Date().getTime();
  } else {
    config.url = config.url + '&_t=' + new Date().getTime();
  }
  
  return config;
};

// 响应拦截器
const responseInterceptor = (response) => {
  // 这里可以根据后端返回的数据结构进行调整
  if (response.statusCode === 200) {
    // 假设后端返回格式为 { code: 0, data: xxx, message: 'xxx' }
    const res = response.data;
    
    if (res.code === 0 || res.code === 200) {
      return res.data;
    } else {
      // 业务错误处理
      showToast(res.message || '请求失败');
      return Promise.reject(res);
    }
  } else {
    // HTTP 错误处理
    handleHttpError(response);
    return Promise.reject(response);
  }
};

// HTTP 错误处理
const handleHttpError = (response) => {
  let message = '请求失败';
  
  switch (response.statusCode) {
    case 400:
      message = '请求错误';
      break;
    case 401:
      message = '未授权，请登录';
      // 可以在这里处理登录过期的逻辑
      uni.navigateTo({
        url: '/pages/login/login'
      });
      break;
    case 403:
      message = '拒绝访问';
      break;
    case 404:
      message = '请求地址出错';
      break;
    case 408:
      message = '请求超时';
      break;
    case 500:
      message = '服务器内部错误';
      break;
    case 501:
      message = '服务未实现';
      break;
    case 502:
      message = '网关错误';
      break;
    case 503:
      message = '服务不可用';
      break;
    case 504:
      message = '网关超时';
      break;
    case 505:
      message = 'HTTP版本不受支持';
      break;
    default:
      message = `未知错误(${response.statusCode})`;
  }
  
  showToast(message);
};

// 显示提示
const showToast = (title) => {
  uni.showToast({
    title: title,
    icon: 'none',
    duration: 2000
  });
};

/**
 * 封装请求函数
 * @param {Object} options - 请求配置
 * @param {string} options.url - 请求地址
 * @param {string} options.method - 请求方法，默认GET
 * @param {Object} options.data - 请求数据
 * @param {Object} options.header - 请求头
 * @param {boolean} options.loading - 是否显示加载提示
 * @param {string} options.loadingText - 加载提示文字
 * @returns {Promise} 请求结果Promise
 */
const request = (options = {}) => {
  // 合并默认配置
  const config = {
    url: options.url,
    method: options.method || 'GET',
    data: options.data || {},
    header: options.header || {},
    timeout: TIMEOUT,
    loading: options.loading !== false, // 默认显示loading
    loadingText: options.loadingText || '加载中...'
  };
  
  // 拼接完整URL
  if (!/^https?:\/\//.test(config.url)) {
    config.url = BASE_URL + config.url;
  }
  
  // 请求拦截
  const interceptedConfig = requestInterceptor(config);
  
  // 显示loading
  if (interceptedConfig.loading) {
    uni.showLoading({
      title: interceptedConfig.loadingText,
      mask: true
    });
  }
  
  // 发起请求
  return new Promise((resolve, reject) => {
    uni.request({
      url: interceptedConfig.url,
      method: interceptedConfig.method,
      data: interceptedConfig.data,
      header: interceptedConfig.header,
      timeout: interceptedConfig.timeout,
      success: (res) => {
        try {
          // 响应拦截处理
          const data = responseInterceptor(res);
          resolve(data);
        } catch (error) {
          reject(error);
        }
      },
      fail: (err) => {
        showToast('网络请求失败');
        reject(err);
      },
      complete: () => {
        // 隐藏loading
        if (interceptedConfig.loading) {
          uni.hideLoading();
        }
      }
    });
  });
};

// 封装常用请求方法
const http = {
  get: (url, data = {}, options = {}) => {
    return request({
      url,
      method: 'GET',
      data,
      ...options
    });
  },
  post: (url, data = {}, options = {}) => {
    return request({
      url,
      method: 'POST',
      data,
      ...options
    });
  },
  put: (url, data = {}, options = {}) => {
    return request({
      url,
      method: 'PUT',
      data,
      ...options
    });
  },
  delete: (url, data = {}, options = {}) => {
    return request({
      url,
      method: 'DELETE',
      data,
      ...options
    });
  },
  // 上传文件
  upload: (url, filePath, formData = {}, options = {}) => {
    const config = {
      url: url.startsWith('http') ? url : BASE_URL + url,
      loading: options.loading !== false,
      loadingText: options.loadingText || '上传中...'
    };
    
    // 请求拦截
    const interceptedConfig = requestInterceptor(config);
    
    // 显示loading
    if (interceptedConfig.loading) {
      uni.showLoading({
        title: interceptedConfig.loadingText,
        mask: true
      });
    }
    
    return new Promise((resolve, reject) => {
      uni.uploadFile({
        url: interceptedConfig.url,
        filePath,
        name: options.name || 'file',
        formData,
        header: interceptedConfig.header,
        success: (res) => {
          if (res.statusCode === 200) {
            // 由于uploadFile返回的是字符串，需要手动转换
            let data = JSON.parse(res.data);
            resolve(data);
          } else {
            handleHttpError(res);
            reject(res);
          }
        },
        fail: (err) => {
          showToast('上传失败');
          reject(err);
        },
        complete: () => {
          if (interceptedConfig.loading) {
            uni.hideLoading();
          }
        }
      });
    });
  }
};

export default http;