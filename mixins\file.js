export default {
	data() {
		return {

		}
	},
	methods: {
		handlerOpenPriviewFile(file) {
			if(file.filePath){
			// #ifdef MP-WEIXIN
			// 访问路径
			// let match = file.filePath.match(/\.([^.]+)$/);
			// if (match) {
			// 	let fileType = match ? match[0] : ''
			// 	console.log("fileType", fileType.split("."))
			// 	uni.openDocument({
			// 		filePath: file.filePath,
			// 		fileType: fileType.split(".")[1],
			// 		fail: function(err) {
			// 			console.log("err", err)
			// 			uni.showToast({
			// 				icon: 'none',
			// 				title: "文件域名请先配置白名单!"
			// 			})
			// 		}
			// 	})
			// } else {
			// 	uni.showToast({
			// 		icon: 'none',
			// 		title: '文件类型有误!'
			// 	})
			// }
			// 下载文件
			uni.showLoading()
			uni.downloadFile({
				url: file.filePath,
				success: function(res) {
					var filePath = res.tempFilePath;
					uni.hideLoading()
					uni.openDocument({
						filePath: filePath,
						showMenu: true,
						success: function(res) {
							console.log('打开文档成功');
						}
					});
				}
			});
			// #endif
			// #ifndef MP-WEIXIN
			window.open(file.filePath, '_blank')
			// #endif
			}
			else {
				uni.showToast({
					title: "该政策暂未提供文件链接",
					icon: "none"
				})
			}
		}
	}
}