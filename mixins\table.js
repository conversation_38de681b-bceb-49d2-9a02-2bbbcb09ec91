export default {
	data() {
		return {
			// v-model绑定的这个变量不要在分页请求结束中自己赋值，直接使用即可
			dataList: [],
			body: {
				exhibitionName: '',
				approveState: '',
				page: 1,
				pageSize: 10,
				total: 0
			},
			api: '/api/third/common/policyFile',
			apiBase: '$sjHttp',
			useQueryPage: true,
			useFunCallApi: '',
			pageKey: 'page'
		}
	},
	methods: {
		clear() {
			this.body.exhibitionName = ''
			this.dataList = []
			this.body[this.pageKey] = 1
			this.fetchList()
		},
		search(val) {
			console.log("val", val)
			// this.body.exhibitionName = val
			this.dataList = []
			this.body.page = 1
			this.fetchList()
		},
		// 列表接口请求
		async fetchList() {
			const data = this.body
			if (!this.useFunCallApi) {
				const url = this.useQueryPage ? `${this.api}/${data.page}/${data.pageSize}` : `${this.api}`
				this[this.apiBase].post(`${url}`, {
					...data
				}).then(res => {
					console.log("政策列表", res)
					const {
						records,
						total
					} = res
					this.body.total = total
					console.log("this.dataList", this.dataList)
					// 判断列表总数是否等于Total
					if (Number(this.dataList.length + 1) <= total) {
						this.dataList.push(...records)
						this.$refs.paging.complete(this.dataList);
					} else this.$refs.paging.complete(true);
				})
			} else {
				this.useFunCallApi(data).then(res => {
					console.log("列表", res)
					const {
						records,
						total
					} = res
					this.body.total = total
					console.log("this.dataList", this.dataList)
					// 判断列表总数是否等于Total
					if (Number(this.dataList.length + 1) <= total) {
						this.dataList.push(...records)
						this.$refs.paging.complete(this.dataList);
					} else this.$refs.paging.complete(true);
				})
			}
		},
		// 下拉刷新被触发
		onRefresh() {
			console.log("下拉刷新")
			this.dataList = []
			this.body.page = 1
			// 告知z-paging下拉刷新结束，这样才可以开始下一次的下拉刷新
			// setTimeout(() => {
			// 	// 1.5秒之后停止刷新动画
			// 	this.$refs.paging.complete(mockLists);
			// }, 1500)
		},
		// 重置
		onRestFetch() {
			console.log("下拉刷新")
			this.dataList = []
			this.body.page = 1
			this.fetchList()
			// 告知z-paging下拉刷新结束，这样才可以开始下一次的下拉刷新
			// setTimeout(() => {
			// 	// 1.5秒之后停止刷新动画
			// 	this.$refs.paging.complete(mockLists);
			// }, 1500)
		},
		queryList(pageNo, pageSize) {
			console.log("arg", pageNo, this.dataList.length, this.body.total)
			this.body.page = pageNo
			this.fetchList()
			// success: 
			// 将请求结果通过complete传给z-paging处理，同时也代表请求结束，这一行必须调用
			// this.$refs.paging.complete(mockLists);
			// err:
			// this.$refs.paging.complete(false);
		},
	}
}