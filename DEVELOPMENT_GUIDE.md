# uni-app 模板开发指南

## 二次开发指南

本文档详细说明如何基于此模板进行二次开发，快速构建你的应用。

## 开发前准备

### 1. 项目初始化

```bash
# 克隆或下载模板
git clone <your-template-repo>
cd uni-app-template

# 安装依赖
npm install
```

### 2. 基础配置

#### 修改应用基本信息
编辑 `manifest.json`：
```json
{
  "name": "你的应用名称",
  "appid": "__UNI__XXXXXXX",
  "description": "应用描述",
  "versionName": "1.0.0",
  "versionCode": "100"
}
```

#### 配置小程序信息
```json
{
  "mp-weixin": {
    "appid": "你的微信小程序AppID",
    "setting": {
      "urlCheck": false
    }
  }
}
```

#### 配置地图服务（如需要）
```json
{
  "mp-weixin": {
    "permission": {
      "scope.userLocation": {
        "desc": "你的位置信息将用于小程序位置接口的效果展示"
      }
    }
  }
}
```

### 3. 环境配置

#### 开发环境配置
编辑 `utils/development.js`：
```javascript
module.exports = {
  baseApi: 'http://localhost:3000/api',  // 你的开发环境API地址
  sjbaseApi: 'http://localhost:3000',    // 文件上传地址
  timeout: 60000,
  debug: true
}
```

#### 生产环境配置
编辑 `utils/production.js`：
```javascript
module.exports = {
  baseApi: 'https://your-domain.com/api',  // 你的生产环境API地址
  sjbaseApi: 'https://your-domain.com',    // 文件上传地址
  timeout: 60000,
  debug: false
}
```

## 核心功能开发

### 1. API 接口开发

#### 添加新的 API 接口
在 `api/rs.js` 中添加：
```javascript
// 用户相关接口
export const getUserInfo = (params) => {
  return request({
    url: '/user/info',
    method: 'GET',
    data: params
  })
}

export const updateUserInfo = (params) => {
  return request({
    url: '/user/update',
    method: 'POST',
    data: params
  })
}
```

#### 接口调用示例
```javascript
import { getUserInfo, updateUserInfo } from '@/api/rs.js'

export default {
  async onLoad() {
    try {
      const userInfo = await getUserInfo({ id: 123 })
      console.log('用户信息：', userInfo)
    } catch (error) {
      console.error('获取用户信息失败：', error)
    }
  }
}
```

### 2. 页面开发

#### 创建新页面
1. 在 `subPages` 目录下创建页面文件夹
2. 创建 `.vue` 文件

```vue
<template>
  <view class="page-container">
    <view class="content">
      <!-- 页面内容 -->
    </view>
  </view>
</template>

<script>
export default {
  name: 'YourPageName',
  data() {
    return {
      // 页面数据
    }
  },
  onLoad(options) {
    // 页面加载时执行
  },
  methods: {
    // 页面方法
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  padding: 20upx;
}
</style>
```

#### 配置页面路由
在 `pages.json` 中添加：
```json
{
  "subPackages": [
    {
      "root": "subPages",
      "pages": [
        {
          "path": "your-page/your-page",
          "style": {
            "navigationBarTitleText": "页面标题"
          }
        }
      ]
    }
  ]
}
```

### 3. 组件开发

#### 创建自定义组件
在 `components` 目录下创建：
```vue
<template>
  <view class="custom-component">
    <slot></slot>
  </view>
</template>

<script>
export default {
  name: 'CustomComponent',
  props: {
    title: {
      type: String,
      default: ''
    }
  }
}
</script>
```

#### 使用组件
```vue
<template>
  <custom-component title="标题">
    内容
  </custom-component>
</template>

<script>
import CustomComponent from '@/components/CustomComponent.vue'

export default {
  components: {
    CustomComponent
  }
}
</script>
```

## 常用功能实现

### 1. 用户登录

```javascript
// 微信登录
async wxLogin() {
  try {
    const loginRes = await uni.login()
    const userInfo = await uni.getUserInfo()
    
    // 发送到后端验证
    const result = await this.loginApi({
      code: loginRes.code,
      userInfo: userInfo
    })
    
    // 保存用户信息
    uni.setStorageSync('token', result.token)
    uni.setStorageSync('userInfo', result.userInfo)
    
  } catch (error) {
    console.error('登录失败：', error)
  }
}
```

### 2. 文件上传

```javascript
// 选择并上传图片
async uploadImage() {
  try {
    const chooseResult = await uni.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera']
    })
    
    const uploadResult = await this.uploadFile({
      filePath: chooseResult.tempFilePaths[0]
    })
    
    console.log('上传成功：', uploadResult)
  } catch (error) {
    console.error('上传失败：', error)
  }
}
```

### 3. 数据缓存

```javascript
// 设置缓存
uni.setStorageSync('key', 'value')

// 获取缓存
const value = uni.getStorageSync('key')

// 清除缓存
uni.removeStorageSync('key')
```

### 4. 页面跳转

```javascript
// 保留当前页面，跳转到应用内的某个页面
uni.navigateTo({
  url: '/subPages/detail/detail?id=123'
})

// 关闭当前页面，跳转到应用内的某个页面
uni.redirectTo({
  url: '/pages/index/index'
})

// 跳转到 tabBar 页面
uni.switchTab({
  url: '/pages/mine/mine'
})
```

## 样式开发规范

### 1. 尺寸单位
- 使用 `upx` 作为主要单位（uni-app 会自动转换）
- 边框等细线使用 `1px`

### 2. 颜色规范
```scss
// 主色调
$primary-color: #007AFF;
$success-color: #4CD964;
$warning-color: #FF9500;
$error-color: #FF3B30;

// 文字颜色
$text-color: #333333;
$text-color-light: #666666;
$text-color-lighter: #999999;
```

### 3. 布局规范
```scss
// 页面容器
.page-container {
  padding: 20upx;
  background-color: #f8f8f8;
  min-height: 100vh;
}

// 卡片样式
.card {
  background-color: #ffffff;
  border-radius: 10upx;
  padding: 20upx;
  margin-bottom: 20upx;
}
```

## 调试和测试

### 1. 开发调试
```javascript
// 开发环境下的调试信息
if (process.env.NODE_ENV === 'development') {
  console.log('调试信息：', data)
}
```

### 2. 错误处理
```javascript
// 全局错误处理
Vue.config.errorHandler = (err, vm, info) => {
  console.error('Vue错误：', err, info)
  // 上报错误信息
}
```

## 性能优化

### 1. 图片优化
- 使用适当的图片格式和尺寸
- 启用图片懒加载
- 使用 CDN 加速

### 2. 代码优化
- 使用分包加载
- 避免在 onLoad 中执行耗时操作
- 合理使用缓存

### 3. 网络优化
- 减少 API 请求次数
- 使用请求缓存
- 实现离线功能

## 发布部署

### 1. 小程序发布
1. 在 HBuilderX 中点击发行 -> 小程序-微信
2. 填写版本号和项目备注
3. 点击发行，等待编译完成
4. 在微信开发者工具中上传代码
5. 在微信公众平台提交审核

### 2. H5 发布
1. 配置 H5 域名和路由模式
2. 点击发行 -> 网站-H5手机版
3. 将生成的 dist 目录部署到服务器

### 3. App 发布
1. 配置 App 图标和启动页
2. 申请相关证书
3. 点击发行 -> 原生App-云打包
4. 选择打包类型和证书
5. 等待打包完成并下载

## 常见问题解决

### Q: 如何解决跨域问题？
A: 在开发环境中配置代理，生产环境确保后端支持 CORS。

### Q: 小程序真机调试异常？
A: 检查域名是否在小程序后台配置，确保使用 HTTPS。

### Q: 如何实现下拉刷新？
A: 使用 `onPullDownRefresh` 生命周期和 `uni.stopPullDownRefresh()`。

### Q: 如何适配不同屏幕尺寸？
A: 使用 `upx` 单位和弹性布局，避免固定宽高。

## 扩展功能

### 1. 集成第三方插件
- 在 uni_modules 目录下安装插件
- 按照插件文档进行配置

### 2. 原生插件开发
- 使用 uni-app 原生插件开发框架
- 编写 iOS/Android 原生代码

### 3. 云函数集成
- 使用 uniCloud 云开发
- 编写云函数处理业务逻辑

通过以上指南，你可以快速基于此模板开发出功能完整的 uni-app 应用。
