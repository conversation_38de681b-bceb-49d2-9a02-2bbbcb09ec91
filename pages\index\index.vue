<template>
	<view class="home-container">
		<!-- 顶部轮播图 -->
		<view class="banner-section">
			<swiper class="banner-swiper" :indicator-dots="true" indicator-active-color="#007AFF" circular :autoplay="true" :interval="3000" :duration="500">
				<swiper-item v-for="(item, index) in bannerList" :key="index">
					<image class="banner-image" :src="item.image" mode="aspectFill" @click="handleBannerClick(item)"></image>
				</swiper-item>
			</swiper>
		</view>
		
		<!-- 通知栏 -->
		<view class="notice-section" v-if="noticeList.length">
			<view class="notice-content">
				<uni-icons type="sound-filled" color="#FF9500" size="20"></uni-icons>
				<swiper class="notice-swiper" vertical :autoplay="true" :interval="3000" :duration="500">
					<swiper-item v-for="(notice, index) in noticeList" :key="index">
						<text class="notice-text" @click="handleNoticeClick(notice)">{{ notice.title }}</text>
					</swiper-item>
				</swiper>
			</view>
		</view>

		<!-- 功能菜单 -->
		<view class="menu-section">
			<view class="section-title">
				<uni-icons type="apps" size="20" color="#333"></uni-icons>
				<text>功能菜单</text>
			</view>
			<view class="menu-grid">
				<view class="menu-item" v-for="(item, index) in menuList" :key="index" @click="handleMenuClick(item)">
					<view class="menu-icon" :style="{backgroundColor: item.color + '20'}">
						<uni-icons :type="item.icon" size="24" :color="item.color"></uni-icons>
					</view>
					<text class="menu-text">{{ item.name }}</text>
				</view>
			</view>
		</view>
		
		<!-- 快捷服务 -->
		<view class="quick-service">
			<view class="section-title">
				<uni-icons type="star" size="20" color="#333"></uni-icons>
				<text>快捷服务</text>
			</view>
			<view class="service-grid">
				<view class="service-item" v-for="(item, index) in serviceList" :key="index" @click="handleServiceClick(item)">
					<view class="service-icon" :style="{backgroundColor: item.color + '15'}">
						<uni-icons :type="item.icon" size="20" :color="item.color"></uni-icons>
					</view>
					<text class="service-text">{{ item.name }}</text>
				</view>
			</view>
		</view>
		
		<!-- 内容列表 -->
		<view class="content-section">
			<view class="section-title">
				<uni-icons type="notification" size="20" color="#333"></uni-icons>
				<text>最新动态</text>
			</view>
			<view class="content-list">
				<view
					class="content-item"
					v-for="(item, index) in contentList"
					:key="index"
					@click="handleContentClick(item)"
				>
					<view class="content-left">
						<view class="content-avatar">
							<uni-icons type="person-filled" size="20" color="#fff"></uni-icons>
						</view>
					</view>
					<view class="content-right">
						<view class="content-title">{{ item.title }}</view>
						<view class="content-summary">{{ item.summary }}</view>
						<view class="content-meta">
							<text class="content-time">{{ item.time }}</text>
							<view class="content-tags">
								<text class="tag-item" v-for="tag in item.tags" :key="tag">{{ tag }}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view class="more-btn" @click="goToList">
				<text>查看更多</text>
				<uni-icons type="right" size="16" color="#999"></uni-icons>
			</view>
		</view>
	</view>
</template>

<script>
import { getDataList } from '@/api/rs.js'

export default {
	name: 'HomePage',
	data() {
		return {
			// 轮播图数据
			bannerList: [
				{
					id: 1,
					image: 'https://picsum.photos/750/300?random=1',
					title: '欢迎使用uni-app模板',
					url: ''
				},
				{
					id: 2,
					image: 'https://picsum.photos/750/300?random=2',
					title: '快速开发跨平台应用',
					url: ''
				},
				{
					id: 3,
					image: 'https://picsum.photos/750/300?random=3',
					title: '支持小程序、H5、App',
					url: ''
				}
			],
			
			// 通知数据
			noticeList: [
				{
					id: 1,
					title: '这是一条重要通知信息'
				},
				{
					id: 2,
					title: '系统维护通知，请注意时间安排'
				}
			],
			
			// 功能菜单数据
			menuList: [
				{
					id: 1,
					name: '示例功能',
					icon: 'gear-filled',
					color: '#007AFF',
					url: '/subPages/demo/demo'
				},
				{
					id: 2,
					name: '列表示例',
					icon: 'list',
					color: '#34C759',
					url: '/subPages/list/list'
				},
				{
					id: 3,
					name: '表单示例',
					icon: 'compose',
					color: '#FF9500',
					url: '/subPages/form/form'
				},
				{
					id: 4,
					name: '图表统计',
					icon: 'bars',
					color: '#FF3B30',
					url: ''
				},
				{
					id: 5,
					name: '消息通知',
					icon: 'chat-filled',
					color: '#5856D6',
					url: ''
				},
				{
					id: 6,
					name: '文件管理',
					icon: 'folder-add',
					color: '#AF52DE',
					url: ''
				},
				{
					id: 7,
					name: '设置中心',
					icon: 'settings',
					color: '#8E8E93',
					url: ''
				},
				{
					id: 8,
					name: '更多功能',
					icon: 'more-filled',
					color: '#6D6D70',
					url: ''
				}
			],
			
			// 快捷服务数据
			serviceList: [
				{
					id: 1,
					name: '在线客服',
					icon: 'headphones',
					color: '#007AFF'
				},
				{
					id: 2,
					name: '意见反馈',
					icon: 'chatbubble',
					color: '#34C759'
				},
				{
					id: 3,
					name: '帮助中心',
					icon: 'help',
					color: '#FF9500'
				},
				{
					id: 4,
					name: '关于我们',
					icon: 'info',
					color: '#5856D6'
				}
			],
			
			// 内容列表数据
			contentList: []
		}
	},
	
	onLoad() {
		this.loadContentList()
	},
	
	methods: {
		// 轮播图点击
		handleBannerClick(item) {
			if (item.url) {
				uni.navigateTo({
					url: item.url
				})
			}
		},
		
		// 通知点击
		handleNoticeClick(notice) {
			uni.showModal({
				title: '通知详情',
				content: notice.title,
				showCancel: false
			})
		},
		
		// 菜单点击
		handleMenuClick(item) {
			if (item.url) {
				uni.navigateTo({
					url: item.url
				})
			} else {
				uni.showToast({
					title: '功能开发中',
					icon: 'none'
				})
			}
		},
		
		// 服务点击
		handleServiceClick(item) {
			uni.showToast({
				title: `点击了${item.name}`,
				icon: 'none'
			})
		},
		
		// 内容点击
		handleContentClick(item) {
			uni.navigateTo({
				url: `/subPages/detail/detail?id=${item.id}`
			})
		},
		
		// 跳转到列表页
		goToList() {
			uni.navigateTo({
				url: '/subPages/list/list'
			})
		},
		
		// 加载内容列表
		async loadContentList() {
			try {
				// 这里可以调用实际的API
				// const result = await getDataList({ page: 1, pageSize: 5 })
				
				// 模拟数据
				this.contentList = [
					{
						id: 1,
						title: 'uni-app 开发技巧分享',
						summary: '分享一些在uni-app开发过程中的实用技巧和最佳实践',
						time: '2024-01-15',
						tags: ['技术', '分享']
					},
					{
						id: 2,
						title: '跨平台开发的优势与挑战',
						summary: '探讨跨平台开发在现代移动应用开发中的重要性',
						time: '2024-01-12',
						tags: ['跨平台', '开发']
					},
					{
						id: 3,
						title: '小程序性能优化指南',
						summary: '详细介绍小程序性能优化的方法和注意事项',
						time: '2024-01-10',
						tags: ['性能', '优化']
					}
				]
			} catch (error) {
				console.error('加载内容失败：', error)
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.home-container {
	background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
	min-height: 100vh;
}

.banner-section {
	height: 400upx;
	margin-top: 88upx;
	padding: 0 30upx;
}

.banner-swiper {
	height: 100%;
	border-radius: 20upx;
	overflow: hidden;
	box-shadow: 0 8upx 32upx rgba(0, 0, 0, 0.1);
}

.banner-image {
	width: 100%;
	height: 100%;
}

.notice-section {
	background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
	padding: 20upx 30upx;
	margin: 20upx 30upx;
	border-radius: 16upx;
	border-left: 8upx solid #FF9500;
	box-shadow: 0 4upx 16upx rgba(255, 149, 0, 0.2);
}

.notice-content {
	display: flex;
	align-items: center;
}

.notice-swiper {
	flex: 1;
	height: 60upx;
	margin-left: 20upx;
}

.notice-text {
	font-size: 28upx;
	color: #856404;
	line-height: 60upx;
	font-weight: 500;
}

.section-title {
	display: flex;
	align-items: center;
	font-size: 36upx;
	font-weight: bold;
	color: #333;
	margin-bottom: 30upx;

	text {
		margin-left: 12upx;
	}
}

.menu-section, .quick-service, .content-section {
	background-color: #ffffff;
	margin: 30upx;
	padding: 40upx;
	border-radius: 24upx;
	box-shadow: 0 8upx 32upx rgba(0, 0, 0, 0.08);
}

.menu-grid {
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
	gap: 20upx;
}

.menu-item {
	width: 22%;
	text-align: center;
	padding: 20upx 10upx;
	border-radius: 16upx;
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.95);
	}
}

.menu-icon {
	width: 80upx;
	height: 80upx;
	margin: 0 auto 16upx;
	border-radius: 20upx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.menu-text {
	font-size: 24upx;
	color: #333;
	font-weight: 500;
}

.service-grid {
	display: flex;
	justify-content: space-around;
}

.service-item {
	text-align: center;
	padding: 20upx;
	border-radius: 16upx;
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.95);
	}
}

.service-icon {
	width: 60upx;
	height: 60upx;
	margin: 0 auto 12upx;
	border-radius: 16upx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.service-text {
	font-size: 24upx;
	color: #333;
	font-weight: 500;
}

.content-list {
	margin-bottom: 20upx;
}

.content-item {
	display: flex;
	padding: 30upx 0;
	border-bottom: 1upx solid #f0f0f0;
	transition: all 0.3s ease;

	&:last-child {
		border-bottom: none;
	}

	&:active {
		background-color: #f8f9fa;
		border-radius: 12upx;
		margin: 0 -20upx;
		padding: 30upx 20upx;
	}
}

.content-left {
	margin-right: 24upx;
}

.content-avatar {
	width: 80upx;
	height: 80upx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 40upx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.content-right {
	flex: 1;
}

.content-title {
	font-size: 32upx;
	color: #333;
	font-weight: 600;
	line-height: 1.4;
	margin-bottom: 12upx;
}

.content-summary {
	font-size: 28upx;
	color: #666;
	line-height: 1.5;
	margin-bottom: 16upx;
}

.content-meta {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.content-time {
	font-size: 24upx;
	color: #999;
}

.content-tags {
	display: flex;
	gap: 12upx;
}

.tag-item {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: #fff;
	padding: 6upx 16upx;
	border-radius: 20upx;
	font-size: 20upx;
	font-weight: 500;
}

.more-btn {
	display: flex;
	justify-content: center;
	align-items: center;
	margin-top: 30upx;
	padding: 20upx;
	background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
	border-radius: 16upx;
	color: #666;
	font-size: 28upx;
	font-weight: 500;
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.98);
		background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
	}

	text {
		margin-right: 8upx;
	}
}
</style>
