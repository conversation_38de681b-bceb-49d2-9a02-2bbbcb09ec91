<template>
  <view class="cell-container" @click="handleClick">
    <view class="cell-content">
      <view class="cell-icon">
        <image src="/static/icons/todo.png" mode="aspectFit" class="icon-image"></image>
      </view>
      <view class="cell-info">
        <text class="cell-title">{{ obj.title }}</text>
        <text class="cell-date">{{ obj.date }}</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'DocCell',
  props: {
    // 文档标题
    title: {
      type: String,
      default: ''
    },
    // 文档日期
    date: {
      type: String,
      default: ''
    },
    // 文档链接或ID
    docId: {
      type: [String, Number],
      default: ''
    },
	obj: {
		type: Object,
		default: () => {}
	}
  },
  methods: {
    // 点击文档卡片时触发
    handleClick() {
      this.$emit('click', this.obj);
    }
  }
}
</script>

<style lang="scss" scoped>
.cell-container {
  background-color: #f0f7ff; /* 浅蓝色背景 */
  border-radius: 8px;
  // padding: 10px;
  margin-bottom: 10px;
}

.cell-content {
  background-color: #ffffff;
  border-radius: 6px;
  padding: 12px;
  display: flex;
  flex-direction: row;
  align-items: center;
}

.cell-icon {
  width: 40px;
  height: 40px;
  margin-right: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.icon-image {
  width: 30px;
  height: 30px;
}

.cell-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.cell-title {
  font-size: 14px;
  color: #333333;
  font-weight: 500;
  line-height: 1.4;
  margin-bottom: 4px;
  /* 超出两行显示省略号 */
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.cell-date {
  font-size: 12px;
  color: #999999;
}
</style>