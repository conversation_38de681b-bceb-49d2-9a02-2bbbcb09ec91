{
	"pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "首页",
				"transparentTitle": "always",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/mine/mine",
			"style": {
				"navigationBarTitleText": "",
				"transparentTitle": "always",
				"navigationStyle": "custom"
			}
		}
	],
	"subPackages": [{
		"root": "subPages",
		"pages": [
			{
				"path": "demo/demo",
				"style": {
					"navigationBarTitleText": "示例页面",
					"navigationStyle": "custom"
				}
			},
			{
				"path": "list/list",
				"style": {
					"navigationBarTitleText": "列表示例",
					"navigationStyle": "custom"
				}
			},
			{
				"path": "detail/detail",
				"style": {
					"navigationBarTitleText": "详情页面"
				}
			},
			{
				"path": "form/form",
				"style": {
					"navigationBarTitleText": "表单页面"
				}
			}
		]
	}],
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "uni-app",
		"navigationBarBackgroundColor": "#F8F8F8",
		"backgroundColor": "#F8F8F8"
	},
	"tabBar": {
		"color": "#7A8499",
		"selectedColor": "#0080FF",
		"borderStyle": "black",
		"backgroundColor": "#FFFFFF",
		"fontSize": "26upx",
		"iconWidth": "30px",
		"height": "60px",
		"list": [{
			"pagePath": "pages/index/index",
			"iconPath": "static/tabar/tab-0.png",
			"selectedIconPath": "static/tabar/tab-1.png",
			"text": "首页"
		}, {
			"pagePath": "pages/mine/mine",
			"iconPath": "static/tabar/tab-1-0.png",
			"selectedIconPath": "static/tabar/tab-1-1.png",
			"text": "我的"
		}]
	},
	"uniIdRouter": {}
}