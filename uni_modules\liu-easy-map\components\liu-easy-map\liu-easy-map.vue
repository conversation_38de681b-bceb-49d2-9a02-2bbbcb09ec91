<template>
	<view style="width: 100%; height: 100%;">
		<!-- nowLat ? nowLat : -->
		<!-- nowLng ? nowLng : -->
		<map style="width: 100%; height: 100%;" id="esaymap" :scale="scale" :latitude="centerLat"
			:longitude="centerLng" :markers="markers" :polygons="polygonsData"
			:enable-zoom="isEnableZoom" :enable-scroll="isEnableScroll" :enable-satellite="isShowWxMap"
			:enable-rotate="isEnableRotate" @markertap="chooseItem" @tap="clickMap" @regionchange="$emit('regionchange')">
		</map>
		<view class="rightbox" v-show="false">
			<view class="boxitem" @click="changeTab(1)">
				<image class="itemimg" :src="tabIndex ? myaddressOnImg : myaddressImg" mode=""></image>
				<view class="itemname" :class="tabIndex ? 'active' : ''">我的位置</view>
			</view>
			<view class="boxitem" @click="changeTab(2)" v-if="wxMapShow">
				<image class="itemimg" :src="tabIndex2 ? wxmapOnImg:wxmapImg" mode=""></image>
				<view class="itemname" :class="tabIndex2 ? 'active' : ''">卫星地图</view>
			</view>
		</view>
		<cover-view class="detailbox" v-if="isShowDetail">
			<!-- <cover-image class="closeicon" :src="closeImg" @click="closeDetail">关闭</cover-image> -->
			<cover-view class="closeicon" @click="closeDetail">关闭</cover-view>
			<cover-view class="boxl">
				<cover-view class="boxlhd ellipsis">{{detailData.name || '--'}}</cover-view>
				<cover-view class="boxlbd ellipsis">{{detailData.address || '--'}}</cover-view>
			</cover-view>
			<cover-view class="boxr" @click="goRoute">去导航</cover-view>
<!-- 			<cover-view class="boxr" @click="goRoute">
				<cover-image class="boxrimg" :src="goImg" mode=""></cover-image>
			</cover-view> -->
		</cover-view>
	</view>
</template>

<script>
	export default {
		props: {
			//中心点纬度
			centerLat: {
				type: [String,Number],
				default: ''
			},
			//中心点经度
			centerLng: {
				type: [String,Number],
				default: ''
			},
			//标记点数据
			markerData: {
				type: Array,
				default () {
					return []
				}
			},
			//多边形数据
			polygons: {
				type: Array,
				default () {
					return []
				}
			},
			//标记点图标宽度
			markerIconWidth: {
				type: Number,
				default: 22
			},
			//标记点图标高度
			markerIconHeight: {
				type: Number,
				default: 32
			},
			//标记点图标路径
			markerIconUrl: {
				type: String,
				default: ''
			},
			//缩放级别 取值范围为3-20
			scale: {
				type: Number,
				default: 16
			},
			//是否显示指南针
			isShowCompass: {
				type: Boolean,
				default: false
			},
			//是否支持缩放
			isEnableZoom: {
				type: Boolean,
				default: true
			},
			//是否支持拖动
			isEnableScroll: {
				type: Boolean,
				default: true
			},
			//是否支持旋转
			isEnableRotate: {
				type: Boolean,
				default: false
			},
			goImgIn: '',
			markerImgIn: '',
			closeIcon: ''
		},
		watch: {
			markerData: {
				immediate: true, //初始化的时候是否调用
				deep: true, //是否开启深度监听
				handler(newValue, oldValue) {
					this.markerDatas = newValue
					this.showMarkers()
				}
			},
			markerImgIn: {
				handler(val) {
					// #ifdef H5
					this.markerImg = val
					// #endif
				},
				immediate: true,
				deep: true
			},
			goImgIn: {
				handler(val) {
					// #ifdef H5
					this.goImg = val
					// #endif
				},
				immediate: true,
				deep: true
			},
			closeIcon: {
				handler(val) {
					// #ifdef H5
					this.closeImg = val
					// #endif
				},
				immediate: true,
				deep: true
			},
			polygons: {
				immediate: true, //初始化的时候是否调用
				deep: true, //是否开启深度监听
				handler(newValue, oldValue) {
					this.polygonsData = [...newValue]
				}
			}
		},
		data() {
			return {
				markerImg: '../../static/marker.png',
				goImg: require('../../static/go.png'),
				myaddressImg: require('../../static/myaddress.png'),
				wxmapImg: require('../../static/wxmap.png'),
				myaddressOnImg: require('../../static/myaddress-on.png'),
				wxmapOnImg: require('../../static/wxmap-on.png'),
				closeImg: require('../../static/close.png'),
				polygonsData: [], //polygons区域数据
				markers: [], //markers数据
				detailData: {}, //选中展示详情数据
				nowLat: '', //我的当前位置
				nowLng: '',
				tabIndex: false,
				tabIndex2: false,
				isShowWxMap: false, //是否展示卫星地图 
				isShowDetail: false, //是否展示详情弹框
				wxMapShow: false, //是否展示卫星地图按钮（小程序展示）
			}
		},
		mounted() {
			const type = uni.getSystemInfoSync().uniPlatform
			if (type == 'mp-weixin') {
				this.wxMapShow = true
			}
			this.showMarkers()
			if (!this.markerData) this.getLocation()
		},
		methods: {
			//右侧类型切换
			changeTab(index) {
				if (index == 1) {
					this.tabIndex = !this.tabIndex
					if (this.tabIndex) this.getLocation()
					else this.showMarkers()
				} else {
					this.tabIndex2 = !this.tabIndex2
					if (this.tabIndex2) this.isShowWxMap = true
					else this.isShowWxMap = false
				}
			},
			//获取当前的地理位置
			getLocation() {
				uni.getLocation({
					type: 'gcj02',
					isHighAccuracy: true,
					highAccuracyExpireTime: 3500,
					success: (res) => {
						console.log("获取地址", res)
						this.nowLat = res.latitude
						this.nowLng = res.longitude
						let arr = [{
							id: 9999,
							latitude: res.latitude || '', //纬度
							longitude: res.longitude || '', //经度
							width: this.markerIconWidth, //宽
							height: this.markerIconHeight, //高
							iconPath: this.markerImg
						}];
						this.markers = [...arr];
						let mapObjs = uni.createMapContext('esaymap', this)
						mapObjs.moveToLocation({
							latitude: res.latitude,
							longitude: res.longitude
						}, {
							complete: res => {}
						})
					},
					fail: (res) => {
						if (res.errMsg == "getLocation:fail auth deny") {
							uni.showModal({
								content: '检测到您没打开获取信息功能权限，是否去设置打开？',
								confirmText: "确认",
								cancelText: '取消',
								success: (res) => {
									if (res.confirm) {
										uni.openSetting({
											success: (res) => {}
										})
									} else {
										return false;
									}
								}
							})
						}
					}
				})
			},
			//到这去
			goRoute() {
				uni.openLocation({
					latitude: +this.detailData.latitude,
					longitude: +this.detailData.longitude,
					scale: 17,
					name: this.detailData.name || '--',
					address: this.detailData.address || '--'
				});
			},
			// 移除marker
			clearMarker(){
				this.markers = []
			},
			//地图打点展示marker
			showMarkers() {
				if (this.markerDatas && this.markerDatas.length > 0) {
					var arr = []
					for (var i = 0; i < this.markerDatas.length; i++) {
						arr.push({
							id: Number(this.markerDatas[i].id),
							latitude: this.markerDatas[i].latitude || '', //纬度
							longitude: this.markerDatas[i].longitude || '', //经度
							iconPath: this.markerDatas[i].markerUrl ? this.markerDatas[i].markerUrl : this
								.markerImg, //显示的图标        
							rotate: 0, // 旋转度数
							width: this.markerDatas[i].iconWidth ? this.markerDatas[i].iconWidth : this
								.markerIconWidth, //宽
							height: this.markerDatas[i].iconHeight ? this.markerDatas[i].iconHeight : this
								.markerIconHeight, //高
							callout: { //自定义标记点上方的气泡窗口 点击有效
								content: this.markerDatas[i].name, //文本
								color: this.markerDatas[i].calloutColor || '#ffffff', //文字颜色
								fontSize: this.markerDatas[i].calloutFontSize || 14, //文本大小
								borderRadius: this.markerDatas[i].calloutBorderRadius || 6, //边框圆角
								padding: this.markerDatas[i].calloutPadding || 6,
								bgColor: this.markerDatas[i].calloutBgColor || '#0B6CFF', //背景颜色
								display: this.markerDatas[i].calloutDisplay || 'BYCLICK', //常显
							},
						})
					}
					this.markers = arr
				}
			},
			//点击标记点
			chooseItem(e) {
				let markerId = e.detail.markerId
				for (var i = 0; i < this.markerDatas.length; i++) {
					if (this.markerDatas[i].id == markerId) {
						this.isShowDetail = true
						this.detailData = this.markerDatas[i]
						this.$emit("clickMarker", this.markerDatas[i])
						break
					}
				}
			},
			//点击地图(仅微信小程序支持)
			clickMap(e) {
				// #ifdef MP-WEIXIN
				let lat = e.detail.latitude.toFixed(5)
				let lng = e.detail.longitude.toFixed(5)
				this.$emit("clickMap", {
					latitude: lat,
					longitude: lng
				})
				// #endif
			},
			//关闭详情弹框
			closeDetail() {
				this.detailData = {}
				this.isShowDetail = false
			}
		}
	}
</script>

<style>
	.rightbox {
		padding: 0 8upx;
		background: #FFFFFF;
		box-shadow: 0upx 4upx 8upx 0upx rgba(200, 200, 200, 0.5);
		border-radius: 14upx;
		position: fixed;
		top: 154upx;
		right: 20upx;
	}

	.boxitem {
		display: flex;
		flex-direction: column;
		text-align: center;
		padding-bottom: 8upx;
		border-bottom: 2upx solid #E4E4E4;
	}

	.itemimg {
		width: 40upx;
		height: 40upx;
		margin: 16upx auto 4upx;
	}

	.itemname {
		font-size: 22upx;
		font-weight: 400;
		color: #333333;
		line-height: 42upx;
	}

	.active {
		color: #2765F1;
	}

	.detailbox {
		display: flex;
		align-items: center;
		justify-content: space-between;
		width: calc(100% - 128upx);
		padding: 36upx 32upx;
		background: #FFFFFF;
		border-radius: 16upx;
		position: fixed;
		bottom: 32upx;
		left: 32upx;
	}

	.closeicon {
		width: 80upx;
		height: 40upx;
		position: absolute;
		right: 16upx;
		top: 12upx;
		/* color: #999 */
	}

	.boxl {
		width: calc(100% - 84upx);
	}

	.boxlhd {
		margin-bottom: 16upx;
		white-space: pre-wrap;
		font-size: 36upx;
		font-weight: bold;
		color: #333333;
		line-height: 48upx;
	}

	.boxlbd {
		font-size: 30upx;
		font-weight: 400;
		color: #333333;
		line-height: 46upx;
		white-space: pre-wrap;
	}

	.boxr {
		width: 96upx;
		display: flex;
		align-items: center;
		position: absolute;
		bottom: 44upx;
		right: 16upx;
		color: #999
	}

/* 	.boxr::before {
		width: 2upx;
		height: 96upx;
		background: #e3e3e3;
		content: "";
		position: relative;
		left: 0;
		z-index: 99;
	} */

	.boxrimg {
		width: 64upx;
		height: 64upx;
		margin: 0 auto;
	}
	.ellipsis {
		white-space: nowrap;
		/* 不换行 */
		overflow: hidden;
		/* 超出部分隐藏 */
		text-overflow: ellipsis;
		width: 100% !important;
		/* 超出部分显示省略号 */
	}
</style>