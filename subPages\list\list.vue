<template>
	<view class="list-container">
		<!-- 导航栏 -->
		<uni-nav-bar 
			:fixed="true" 
			:shadow="true" 
			:border="false"
			background-color="#007AFF"
			color="#FFFFFF"
			title="列表示例"
			left-icon="left"
			@clickLeft="goBack"
		/>
		
		<!-- 搜索栏 -->
		<view class="search-container">
			<uni-search-bar 
				v-model="searchKeyword"
				placeholder="搜索内容"
				clearButton="true"
				@confirm="handleSearch"
				@clear="handleClear"
			/>
		</view>
		
		<!-- 列表内容 -->
		<z-paging 
			ref="paging" 
			v-model="dataList" 
			@query="queryList"
			:refresher-enabled="true"
			:auto="true"
		>
			<view class="list-content">
				<view
					class="list-item"
					v-for="(item, index) in dataList"
					:key="index"
					@click="handleItemClick(item)"
				>
					<view class="item-header">
						<image :src="item.image" mode="aspectFill" class="item-image"></image>
						<view class="item-info">
							<view class="item-title">{{ item.title }}</view>
							<view class="item-subtitle">{{ item.subtitle }}</view>
							<view class="item-meta">
								<text class="item-time">{{ item.time }}</text>
								<view class="item-status" :class="getStatusClass(item.status)">
									{{ item.status }}
								</view>
							</view>
						</view>
					</view>
					<view class="item-content">{{ item.content }}</view>
					<view class="item-footer">
						<view class="item-stats">
							<view class="stat-item">
								<uni-icons type="heart" size="16" color="#999"></uni-icons>
								<text>{{ item.likes }}</text>
							</view>
							<view class="stat-item">
								<uni-icons type="eye" size="16" color="#999"></uni-icons>
								<text>{{ item.views }}</text>
							</view>
						</view>
						<view class="item-actions">
							<button class="action-btn" @click.stop="handleEdit(item)">
								<uni-icons type="compose" size="16" color="#007AFF"></uni-icons>
							</button>
							<button class="action-btn" @click.stop="handleDelete(item)">
								<uni-icons type="trash" size="16" color="#FF3B30"></uni-icons>
							</button>
						</view>
					</view>
				</view>
			</view>
		</z-paging>
		
		<!-- 浮动按钮 -->
		<uni-fab 
			:pattern="fabPattern" 
			:content="fabContent"
			@trigger="handleFabClick"
		/>
	</view>
</template>

<script>
import { getDataList, deleteData } from '@/api/rs.js'

export default {
	name: 'ListPage',
	data() {
		return {
			searchKeyword: '',
			dataList: [],
			fabPattern: {
				color: '#007AFF',
				backgroundColor: '#fff',
				selectedColor: '#007AFF'
			},
			fabContent: [
				{
					iconPath: '/static/icons/add.png',
					selectedIconPath: '/static/icons/add-active.png',
					text: '添加',
					active: false
				}
			]
		}
	},
	methods: {
		// 返回上一页
		goBack() {
			uni.navigateBack()
		},
		
		// 查询列表数据
		async queryList(pageNo, pageSize) {
			try {
				const params = {
					page: pageNo,
					pageSize: pageSize,
					keyword: this.searchKeyword
				}
				
				const result = await getDataList(params)
				
				// 模拟数据结构
				const mockData = []
				const categories = ['前端开发', '后端开发', '移动开发', '数据科学', '人工智能']
				const statuses = ['热门', '推荐', '最新', '精选']

				for (let i = 0; i < pageSize; i++) {
					const index = (pageNo - 1) * pageSize + i + 1
					mockData.push({
						id: index,
						title: `技术文章标题 ${index}`,
						subtitle: categories[index % categories.length],
						content: `这是一篇关于${categories[index % categories.length]}的技术文章，内容详细介绍了相关的技术要点和实践经验。`,
						time: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toLocaleDateString(),
						image: `https://picsum.photos/120/80?random=${index}`,
						status: statuses[index % statuses.length],
						likes: Math.floor(Math.random() * 500) + 50,
						views: Math.floor(Math.random() * 5000) + 100
					})
				}
				
				this.$refs.paging.complete(mockData)
			} catch (error) {
				console.error('查询列表失败：', error)
				this.$refs.paging.complete(false)
			}
		},
		
		// 搜索
		handleSearch() {
			this.$refs.paging.reload()
		},
		
		// 清空搜索
		handleClear() {
			this.searchKeyword = ''
			this.$refs.paging.reload()
		},
		
		// 列表项点击
		handleItemClick(item) {
			uni.navigateTo({
				url: `/subPages/detail/detail?id=${item.id}`
			})
		},
		
		// 编辑
		handleEdit(item) {
			uni.navigateTo({
				url: `/subPages/form/form?id=${item.id}&mode=edit`
			})
		},
		
		// 删除
		handleDelete(item) {
			uni.showModal({
				title: '确认删除',
				content: `确定要删除"${item.title}"吗？`,
				success: async (res) => {
					if (res.confirm) {
						try {
							await deleteData(item.id)
							uni.showToast({
								title: '删除成功',
								icon: 'success'
							})
							this.$refs.paging.reload()
						} catch (error) {
							console.error('删除失败：', error)
							uni.showToast({
								title: '删除失败',
								icon: 'error'
							})
						}
					}
				}
			})
		},
		
		// 浮动按钮点击
		handleFabClick(e) {
			if (e.index === 0) {
				// 添加新项
				uni.navigateTo({
					url: '/subPages/form/form?mode=add'
				})
			}
		},

		// 获取状态样式类
		getStatusClass(status) {
			const statusMap = {
				'热门': 'status-hot',
				'推荐': 'status-recommend',
				'最新': 'status-new',
				'精选': 'status-featured'
			}
			return statusMap[status] || 'status-default'
		}
	}
}
</script>

<style lang="scss" scoped>
.list-container {
	background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
	min-height: 100vh;
}

.search-container {
	padding: 20upx 30upx;
	background-color: #ffffff;
	border-bottom: 1upx solid #e5e5e5;
	margin-top: 88upx;
	box-shadow: 0 2upx 8upx rgba(0, 0, 0, 0.1);
}

.list-content {
	padding: 30upx;
}

.list-item {
	background-color: #ffffff;
	border-radius: 24upx;
	padding: 30upx;
	margin-bottom: 24upx;
	box-shadow: 0 8upx 32upx rgba(0, 0, 0, 0.08);
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.98);
		box-shadow: 0 4upx 16upx rgba(0, 0, 0, 0.12);
	}
}

.item-header {
	display: flex;
	margin-bottom: 24upx;
}

.item-image {
	width: 120upx;
	height: 80upx;
	border-radius: 12upx;
	margin-right: 24upx;
}

.item-info {
	flex: 1;
}

.item-title {
	font-size: 32upx;
	font-weight: 600;
	color: #333;
	line-height: 1.4;
	margin-bottom: 8upx;
}

.item-subtitle {
	font-size: 24upx;
	color: #666;
	margin-bottom: 12upx;
}

.item-meta {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.item-time {
	font-size: 24upx;
	color: #999;
}

.item-status {
	padding: 6upx 16upx;
	border-radius: 20upx;
	font-size: 20upx;
	font-weight: 500;

	&.status-hot {
		background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
		color: #fff;
	}

	&.status-recommend {
		background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
		color: #fff;
	}

	&.status-new {
		background: linear-gradient(135deg, #45b7d1 0%, #96c93d 100%);
		color: #fff;
	}

	&.status-featured {
		background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
		color: #fff;
	}

	&.status-default {
		background-color: #f0f0f0;
		color: #666;
	}
}

.item-content {
	font-size: 28upx;
	color: #666;
	line-height: 1.6;
	margin-bottom: 24upx;
}

.item-footer {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding-top: 20upx;
	border-top: 1upx solid #f0f0f0;
}

.item-stats {
	display: flex;
	gap: 32upx;
}

.stat-item {
	display: flex;
	align-items: center;
	gap: 8upx;
	font-size: 24upx;
	color: #999;
}

.item-actions {
	display: flex;
	gap: 16upx;
}

.action-btn {
	width: 60upx;
	height: 60upx;
	border-radius: 30upx;
	background-color: #f8f9fa;
	border: none;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.9);
	}
}
</style>
