<template>
	<view class="detail-container">
		<!-- 加载状态 -->
		<uni-load-more v-if="loading" status="loading" />
		
		<!-- 详情内容 -->
		<view v-else-if="detailData" class="detail-content">
			<!-- 头部信息 -->
			<view class="detail-header">
				<view class="title">{{ detailData.title }}</view>
				<view class="meta-info">
					<text class="time">{{ detailData.createTime }}</text>
					<text class="author">作者：{{ detailData.author }}</text>
				</view>
			</view>
			
			<!-- 主要内容 -->
			<view class="detail-body">
				<!-- 图片展示 -->
				<view v-if="detailData.images && detailData.images.length" class="image-section">
					<swiper class="image-swiper" :indicator-dots="true" :autoplay="false">
						<swiper-item v-for="(image, index) in detailData.images" :key="index">
							<image 
								:src="image" 
								mode="aspectFill" 
								class="detail-image"
								@click="previewImage(image, detailData.images)"
							/>
						</swiper-item>
					</swiper>
				</view>
				
				<!-- 文本内容 -->
				<view class="text-content">
					<rich-text :nodes="detailData.content"></rich-text>
				</view>
				
				<!-- 标签 -->
				<view v-if="detailData.tags && detailData.tags.length" class="tags-section">
					<view class="tag-item" v-for="tag in detailData.tags" :key="tag">
						{{ tag }}
					</view>
				</view>
			</view>
			
			<!-- 统计信息 -->
			<view class="detail-stats">
				<view class="stat-item" @click="toggleLike">
					<uni-icons :type="isLiked ? 'heart-filled' : 'heart'" :color="isLiked ? '#ff5722' : '#999'" />
					<text>{{ detailData.likeCount }}</text>
				</view>
				<view class="stat-item" @click="showComments">
					<uni-icons type="chat" color="#999" />
					<text>{{ detailData.commentCount }}</text>
				</view>
				<view class="stat-item" @click="shareContent">
					<uni-icons type="redo" color="#999" />
					<text>分享</text>
				</view>
			</view>
			
			<!-- 相关推荐 -->
			<view v-if="relatedList.length" class="related-section">
				<view class="section-title">相关推荐</view>
				<view class="related-list">
					<view 
						class="related-item" 
						v-for="item in relatedList" 
						:key="item.id"
						@click="goToDetail(item.id)"
					>
						<image :src="item.cover" mode="aspectFill" class="related-cover" />
						<view class="related-info">
							<text class="related-title">{{ item.title }}</text>
							<text class="related-time">{{ item.createTime }}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 错误状态 -->
		<view v-else class="error-state">
			<uni-load-more status="error" @clickLoadMore="loadDetail" />
		</view>
		
		<!-- 底部操作栏 -->
		<view class="bottom-actions">
			<button class="action-btn primary" @click="handleAction">主要操作</button>
			<button class="action-btn secondary" @click="handleSecondaryAction">次要操作</button>
		</view>
	</view>
</template>

<script>
import { getDataDetail, toggleLikeApi } from '@/api/rs.js'

export default {
	name: 'DetailPage',
	data() {
		return {
			id: '',
			loading: true,
			detailData: null,
			relatedList: [],
			isLiked: false
		}
	},
	
	onLoad(options) {
		this.id = options.id
		if (this.id) {
			this.loadDetail()
		}
	},
	
	// 下拉刷新
	onPullDownRefresh() {
		this.loadDetail().finally(() => {
			uni.stopPullDownRefresh()
		})
	},
	
	// 分享
	onShareAppMessage() {
		return {
			title: this.detailData?.title || '详情页面',
			path: `/subPages/detail/detail?id=${this.id}`
		}
	},
	
	methods: {
		// 加载详情数据
		async loadDetail() {
			try {
				this.loading = true
				
				// 模拟API调用
				// const result = await getDataDetail({ id: this.id })
				
				// 模拟数据
				await new Promise(resolve => setTimeout(resolve, 1000))
				
				this.detailData = {
					id: this.id,
					title: 'Vue.js 3.0 Composition API 深度解析',
					content: '<p>Vue.js 3.0 引入了全新的 Composition API，这是一个基于函数的 API，可以更灵活地组织组件逻辑。</p><p>Composition API 的核心思想是将相关的逻辑组织在一起，而不是按照选项类型分离。这样可以提高代码的可读性和可维护性。</p><p>本文将深入探讨 Composition API 的使用方法和最佳实践。</p>',
					author: '前端技术专家',
					createTime: '2024-01-15 14:30:00',
					likeCount: 256,
					commentCount: 48,
					images: [
						'https://picsum.photos/800/400?random=10',
						'https://picsum.photos/800/400?random=11',
						'https://picsum.photos/800/400?random=12'
					],
					tags: ['Vue.js', 'Composition API', '前端开发']
				}
				
				// 加载相关推荐
				this.relatedList = [
					{
						id: '2',
						title: 'React Hooks 最佳实践指南',
						cover: 'https://picsum.photos/200/120?random=20',
						createTime: '2024-01-12',
						author: 'React专家'
					},
					{
						id: '3',
						title: 'TypeScript 进阶技巧分享',
						cover: 'https://picsum.photos/200/120?random=21',
						createTime: '2024-01-10',
						author: 'TS开发者'
					},
					{
						id: '4',
						title: '现代前端工程化实践',
						cover: 'https://picsum.photos/200/120?random=22',
						createTime: '2024-01-08',
						author: '工程化专家'
					}
				]
				
				// 检查是否已点赞
				this.isLiked = uni.getStorageSync(`liked_${this.id}`) || false
				
			} catch (error) {
				console.error('加载详情失败：', error)
				this.detailData = null
			} finally {
				this.loading = false
			}
		},
		
		// 预览图片
		previewImage(current, urls) {
			uni.previewImage({
				current,
				urls
			})
		},
		
		// 切换点赞状态
		async toggleLike() {
			try {
				// 调用点赞API
				// await toggleLikeApi({ id: this.id, action: this.isLiked ? 'unlike' : 'like' })
				
				this.isLiked = !this.isLiked
				this.detailData.likeCount += this.isLiked ? 1 : -1
				
				// 保存点赞状态
				uni.setStorageSync(`liked_${this.id}`, this.isLiked)
				
				uni.showToast({
					title: this.isLiked ? '点赞成功' : '取消点赞',
					icon: 'none'
				})
				
			} catch (error) {
				console.error('点赞操作失败：', error)
			}
		},
		
		// 显示评论
		showComments() {
			uni.showToast({
				title: '评论功能开发中',
				icon: 'none'
			})
		},
		
		// 分享内容
		shareContent() {
			// #ifdef MP-WEIXIN
			uni.showShareMenu({
				withShareTicket: true
			})
			// #endif
			
			// #ifdef H5
			uni.showToast({
				title: '请使用右上角分享',
				icon: 'none'
			})
			// #endif
		},
		
		// 跳转到其他详情
		goToDetail(id) {
			uni.navigateTo({
				url: `/subPages/detail/detail?id=${id}`
			})
		},
		
		// 主要操作
		handleAction() {
			uni.showModal({
				title: '操作确认',
				content: '确定要执行此操作吗？',
				success: (res) => {
					if (res.confirm) {
						uni.showToast({
							title: '操作成功',
							icon: 'success'
						})
					}
				}
			})
		},
		
		// 次要操作
		handleSecondaryAction() {
			uni.showActionSheet({
				itemList: ['选项1', '选项2', '选项3'],
				success: (res) => {
					uni.showToast({
						title: `选择了选项${res.tapIndex + 1}`,
						icon: 'none'
					})
				}
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.detail-container {
	background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
	min-height: 100vh;
	padding-bottom: 120upx;
}

.detail-content {
	background-color: #ffffff;
	border-radius: 24upx 24upx 0 0;
	margin-top: 20upx;
	overflow: hidden;
	box-shadow: 0 -4upx 32upx rgba(0, 0, 0, 0.1);
}

.detail-header {
	padding: 40upx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: #fff;
}

.title {
	font-size: 40upx;
	font-weight: bold;
	color: #fff;
	line-height: 1.4;
	margin-bottom: 24upx;
}

.meta-info {
	display: flex;
	justify-content: space-between;
	font-size: 26upx;
	color: rgba(255, 255, 255, 0.8);
}

.detail-body {
	padding: 40upx;
}

.image-section {
	margin-bottom: 40upx;
}

.image-swiper {
	height: 400upx;
	border-radius: 16upx;
	overflow: hidden;
	box-shadow: 0 8upx 32upx rgba(0, 0, 0, 0.1);
}

.detail-image {
	width: 100%;
	height: 100%;
}

.text-content {
	line-height: 1.8;
	color: #333;
	margin-bottom: 40upx;
	font-size: 30upx;
}

.tags-section {
	display: flex;
	flex-wrap: wrap;
	gap: 16upx;
	margin-bottom: 30upx;
}

.tag-item {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: #fff;
	padding: 12upx 24upx;
	border-radius: 24upx;
	font-size: 24upx;
	font-weight: 500;
}

.detail-stats {
	display: flex;
	justify-content: space-around;
	padding: 40upx;
	background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
	border-radius: 20upx;
	margin: 0 40upx 40upx;
}

.stat-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 12upx;
	font-size: 26upx;
	color: #666;
	padding: 20upx;
	border-radius: 16upx;
	transition: all 0.3s ease;

	&:active {
		background-color: rgba(255, 255, 255, 0.8);
		transform: scale(0.95);
	}
}

.related-section {
	padding: 30upx;
}

.section-title {
	font-size: 32upx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20upx;
}

.related-item {
	display: flex;
	margin-bottom: 20upx;
	padding: 20upx;
	background-color: #f8f8f8;
	border-radius: 10upx;
}

.related-cover {
	width: 120upx;
	height: 80upx;
	border-radius: 6upx;
	margin-right: 20upx;
}

.related-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.related-title {
	font-size: 28upx;
	color: #333;
	line-height: 1.3;
}

.related-time {
	font-size: 24upx;
	color: #999;
}

.error-state {
	padding: 100upx 0;
	text-align: center;
}

.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	display: flex;
	padding: 20upx;
	background-color: #ffffff;
	border-top: 1upx solid #f0f0f0;
	gap: 20upx;
}

.action-btn {
	flex: 1;
	height: 80upx;
	border-radius: 40upx;
	font-size: 30upx;
}

.action-btn.primary {
	background-color: #007AFF;
	color: #ffffff;
}

.action-btn.secondary {
	background-color: #f0f0f0;
	color: #333;
}
</style>
