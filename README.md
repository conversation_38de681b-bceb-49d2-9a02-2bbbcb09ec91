# uni-app 开发模板

这是一个基于 uni-app 框架的标准开发模板，适用于快速开发跨平台应用（小程序、H5、App）。

## 项目特性

- ✅ 基于 uni-app 框架，支持多端发布
- ✅ 完整的项目结构和代码规范
- ✅ 封装好的 HTTP 请求库
- ✅ 环境配置管理（开发/生产）
- ✅ 常用组件和页面模板
- ✅ 统一的错误处理和用户反馈
- ✅ 响应式设计，适配多种屏幕尺寸

## 项目结构

```
├── api/                    # API接口管理
│   └── rs.js              # 接口定义文件
├── components/            # 公共组件
├── mixins/               # 混入文件
├── pages/                # 主包页面
│   ├── index/            # 首页
│   └── mine/             # 我的页面
├── subPages/             # 分包页面
│   ├── demo/             # 示例页面
│   ├── list/             # 列表页面
│   ├── detail/           # 详情页面
│   └── form/             # 表单页面
├── static/               # 静态资源
│   ├── images/           # 图片资源
│   └── icons/            # 图标资源
├── utils/                # 工具类
│   ├── request.js        # HTTP请求封装
│   ├── development.js    # 开发环境配置
│   └── production.js     # 生产环境配置
├── uni_modules/          # uni-app插件
├── App.vue              # 应用入口
├── main.js              # 主入口文件
├── manifest.json        # 应用配置
├── pages.json           # 页面路由配置
└── uni.scss             # 全局样式
```

## 快速开始

### 1. 环境准备

- Node.js 16+
- HBuilderX 或 VS Code + uni-app插件
- 微信开发者工具（如需开发小程序）

### 2. 安装依赖

```bash
# 如果使用npm
npm install

# 如果使用yarn
yarn install
```

### 3. 配置项目

#### 修改应用信息
编辑 `manifest.json` 文件：
```json
{
  "name": "你的应用名称",
  "appid": "你的应用ID",
  "description": "应用描述"
}
```

#### 配置API接口
编辑环境配置文件：
- 开发环境：`utils/development.js`
- 生产环境：`utils/production.js`

```javascript
module.exports = {
  baseApi: 'https://your-api-domain.com/api',
  timeout: 60000,
  debug: true
}
```

#### 配置小程序
如需发布小程序，需要配置：
```json
{
  "mp-weixin": {
    "appid": "你的小程序AppID"
  }
}
```

### 4. 开发调试

在 HBuilderX 中：
1. 打开项目
2. 选择运行 -> 运行到浏览器/小程序模拟器
3. 开始开发

## 核心功能说明

### HTTP 请求封装

项目已封装完整的 HTTP 请求库，支持：
- 请求/响应拦截器
- 自动 Token 处理
- 统一错误处理
- Loading 状态管理
- 文件上传

使用示例：
```javascript
import { getDataList, createData } from '@/api/rs.js'

// GET 请求
const data = await getDataList({ page: 1, pageSize: 10 })

// POST 请求
const result = await createData({ name: '测试数据' })
```

### 页面模板

项目提供了常用的页面模板：
- **首页模板**：轮播图、菜单、列表展示
- **列表页面**：支持搜索、分页、下拉刷新
- **详情页面**：数据展示和操作
- **表单页面**：数据录入和验证

### 组件使用

项目集成了 uni-ui 组件库，可直接使用：
```vue
<template>
  <uni-list>
    <uni-list-item title="标题" note="描述" />
  </uni-list>
</template>
```

## 开发规范

### 代码规范
- 使用 ES6+ 语法
- 组件名使用 PascalCase
- 文件名使用 kebab-case
- 变量名使用 camelCase

### 目录规范
- 页面文件放在对应的页面目录下
- 公共组件放在 `components` 目录
- 工具函数放在 `utils` 目录
- API 接口放在 `api` 目录

### 样式规范
- 使用 SCSS 预处理器
- 尺寸单位使用 upx（uni-app 单位）
- 颜色使用统一的设计规范

## 部署发布

### 小程序发布
1. 在 HBuilderX 中选择发行 -> 小程序-微信
2. 上传到微信开发者工具
3. 提交审核

### H5 发布
1. 在 HBuilderX 中选择发行 -> 网站-H5手机版
2. 将 dist 目录部署到服务器

### App 发布
1. 配置原生插件和证书
2. 在 HBuilderX 中选择发行 -> 原生App-云打包
3. 下载安装包进行测试

## 常见问题

### Q: 如何添加新的 API 接口？
A: 在 `api/rs.js` 文件中添加新的接口函数，参考现有的接口格式。

### Q: 如何添加新页面？
A: 
1. 在对应目录创建页面文件
2. 在 `pages.json` 中配置路由
3. 根据需要添加到 tabBar 或分包中

### Q: 如何自定义主题色？
A: 修改 `uni.scss` 文件中的 CSS 变量，或在 `App.vue` 中定义全局样式。

## 技术支持

如有问题，请查看：
- [uni-app 官方文档](https://uniapp.dcloud.io/)
- [uni-ui 组件库](https://uniapp.dcloud.io/component/uniui/uni-ui)

## 更新日志

### v1.0.0
- 初始版本发布
- 基础项目结构搭建
- 核心功能实现
