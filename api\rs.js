import http from "../utils/request"
import httpSj from "../utils/requestSj.js"

/**
 * API接口示例模板
 * 请根据实际业务需求修改以下接口
 */

// ==================== 用户相关接口 ====================

// 用户登录
export function userLogin(data) {
	return http.post('/api/user/login', data)
}

// 获取用户信息
export function getUserInfo() {
	return http.get('/api/user/info')
}

// 更新用户信息
export function updateUserInfo(data) {
	return http.post('/api/user/update', data)
}

// 用户退出登录
export function userLogout() {
	return http.post('/api/user/logout')
}

// ==================== 数据列表相关接口 ====================

// 获取列表数据（带分页）
export function getDataList(params) {
	return http.get('/api/data/list', params)
}

// 获取详情数据
export function getDataDetail(id) {
	return http.get(`/api/data/detail/${id}`)
}

// 创建数据
export function createData(data) {
	return http.post('/api/data/create', data)
}

// 更新数据
export function updateData(id, data) {
	return http.put(`/api/data/update/${id}`, data)
}

// 删除数据
export function deleteData(id) {
	return http.delete(`/api/data/delete/${id}`)
}

// ==================== 文件上传相关接口 ====================

// 上传文件
export function uploadFile(filePath, formData = {}) {
	return http.upload('/api/upload/file', filePath, formData)
}

// ==================== 其他通用接口 ====================

// 获取字典数据
export function getDictionary(type) {
	return http.get(`/api/dictionary/${type}`)
}

// 获取配置信息
export function getConfig() {
	return http.get('/api/config')
}