<template>
	<view class="search-container">
		<!-- 返回按钮和搜索区域 -->
		<view class="search-header">
			<!-- 街道选择下拉菜单 -->
			<view class="dropdown-area">
				<view class="dropdown-trigger" @click="toggleStreetDropdown">
					<text class="dropdown-text">{{selectedStreet.text}}</text>
					<uni-icons type="bottom" size="14" color="#000"></uni-icons>
				</view>
				<view class="dropdown-menu" v-if="showStreetDropdown">
					<view class="dropdown-item" v-for="(item, index) in streetList" :key="index"
						@click="selectStreet(item)">
						<text>{{ item.text }}</text>
					</view>
				</view>
			</view>

			<!-- 搜索输入框 -->
			<view class="search-input-area">
				<input type="text" class="search-input" placeholder="请输入关键字" v-model="searchKeyword" />
			</view>

			<!-- 搜索按钮 -->
			<view class="search-btn" @click="handleSearch">
				<text>搜索</text>
			</view>
		</view>
	</view>
</template>

<script>
	import { getDictionary } from "@/api/rs.js"
	export default {
		data() {
			return {
				searchKeyword: '',
				showStreetDropdown: false,
				selectedStreet: {
					text: '全部街道',
					value: 0
				},
				streetList: [{
						text: '全部街道',
						value: 0
					},
					{
						text: '东海街道',
						value: 1
					},
					{
						text: '城东街道',
						value: 2
					},
					{
						text: '城西街道',
						value: 3
					},
					{
						text: '北峰街道',
						value: 4
					},
					{
						text: '华大街道',
						value: 5
					},
					{
						text: '东湖街道',
						value: 6
					},
					{
						text: '丰泽街道',
						value: 7
					},
					{
						text: '泉秀街道',
						value: 8
					},
					{
						text: '清源街道',
						value: 9
					},
					{
						text: '临海街道',
						value: 10
					}
				]
			};
		},
		methods: {
			// 切换街道下拉菜单显示状态
			toggleStreetDropdown() {
				this.showStreetDropdown = !this.showStreetDropdown;
			},

			// 选择街道
			selectStreet(street) {
				this.selectedStreet = street;
				this.showStreetDropdown = false;
				this.$emit("onSelect", street)
			},

			// 处理搜索
			handleSearch() {
				// 触发搜索事件，将搜索关键词和选中的街道传递给父组件
				this.$emit('onSearch', {
					keyword: this.searchKeyword,
					street: this.selectedStreet
				});
			},
		},
		created() {
			getDictionary("zeroStreet").then((res) => {
				console.log("actions", res)
				this.streetList = res.map(el => {
					return {
						text: el.entryName,
						value: el.entryCode
					}
				});
				this.streetList.unshift({
					text: '全部街道',
					value: ''
				});
			});
		}
	};
</script>

<style>
	.search-container {
		width: 100%;
	}

	.search-header {
		display: flex;
		align-items: center;
		background-color: #fff;
		padding: 10px;
		position: relative;
	}

	.back-btn {
		width: 30px;
		height: 30px;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.dropdown-area {
		position: relative;
		margin-right: 10px;
	}

	.dropdown-trigger {
		display: flex;
		align-items: center;
		padding: 0 5px;
		height: 36px;
	}

	.dropdown-text {
		color: #000;
		font-size: 14px;
		margin-right: 5px;
	}

	.dropdown-menu {
		position: absolute;
		top: 40px;
		left: 0;
		background-color: #FFFFFF;
		border-radius: 4px;
		box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
		z-index: 100;
		width: 120px;
	}

	.dropdown-item {
		padding: 10px 15px;
		font-size: 14px;
		color: #333;
		border-bottom: 1px solid #f5f5f5;
	}

	.dropdown-item:last-child {
		border-bottom: none;
	}

	.search-input-area {
		flex: 1;
		background-color: #FFFFFF;
		border-radius: 4px;
		height: 36px;
		display: flex;
		align-items: center;
		padding: 0 10px;
	}

	.search-input {
		width: 100%;
		height: 100%;
		font-size: 14px;
	}

	.search-btn {
		width: 60px;
		height: 36px;
		background-color: #FFFFFF;
		border-radius: 4px;
		margin-left: 10px;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.search-btn text {
		color: #4a89dc;
		font-size: 14px;
		font-weight: bold;
	}
</style>