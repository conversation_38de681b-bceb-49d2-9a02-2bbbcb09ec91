export default {
	data: () => {
		return {
			// 通用静态数据
			dictSexOptions: [{
				text: '男',
				value: 1
			}, {
				text: '女',
				value: 2
			}],
			// 登录页校验拦截
			configLogin: {
				whiteList: ['/pages/login/login'],
				loginPage: '/pages/login/login'
			}
		}
	},
	onShareAppMessage() {
		const pages = getCurrentPages();
		const currentPage = pages[pages.length - 1];

		// 获取页面路径
		const pagePath = currentPage.route;

		// 获取页面名称（不包含路径参数）
		const pageName = currentPage.options ? currentPage.options.name : '';
		return {
			title: '分享给微信好友',
			path: pagePath
		};
	},
	onShareTimeline() {
		const pages = getCurrentPages();
		const currentPage = pages[pages.length - 1];

		// 获取页面路径
		const pagePath = currentPage.route;

		// 获取页面名称（不包含路径参数）
		const pageName = currentPage.options ? currentPage.options.name : '';
		wx.shareToTimeline({
			title: '分享到朋友圈',
			link: pagePath,
			imgUrl: 'https://s1.xiaomiev.com/activity-outer-assets/0328/images/Ultra_U/pc/home_ultra.jpg',
			success: function() {
				console.log('分享到朋友圈成功');
			},
			fail: function(err) {
				console.log('分享到朋友圈失败', err);
			}
		});
	},
	methods: {
		//UNI-UI上传文件通用
		fileProgress(val) {
			console.log("fileProgress", val)
		},
		fileSuccess(val) {
			console.log("fileSuccess", val)
		},
		fileFail(val) {
			console.log("fileFail", val)
		},
		fileSelect(val) {
			console.log("fileSelect", val)
		},
		// UNIapi上传文件方式通用
		// 自定义上传附件 options 参考uni官网文档
		uploadFileApi(options, size = null, baseApi, onSuccess, onFail) {

			console.log("uploadFile", options)
			// #ifdef MP-WEIXIN
			// 聊天文件中获取
			if (options.userFileType === 'file') {
				wx.chooseMessageFile({
					count: 1,
					type: 'file',
					success: (res) => {
						console.log("聊天文件", res)
						const {
							tempFiles
						} = res
						if (size && tempFiles[0].size > size * 1024 * 1024) return uni.showToast({
							title: `文件大小不可超过${size}MB`,
							icon: 'none'
						})
						const actionUrl = `${baseApi}/api/zero/wx/user/uploadAvatar`;
						uni.showLoading({
							mask: true
						})
						uni.uploadFile({
							url: actionUrl, // 你的上传接口地址
							filePath: tempFiles[0].path,
							name: 'file', // 这里根据后端需要的字段来定义
							formData: {
								'file': tempFiles[0], // 其他要传的参数可以在这里添加
								'name': tempFiles[0].name
							},
							success: uploadFileRes => {
								let res = JSON.parse(uploadFileRes.data)
								console.log(res);
								onSuccess({
									filePath: res?.data,
									fileName: tempFiles[0].name
								})
								// 处理上传成功后的结果
							},
							fail: uploadFileError => {
								// 处理上传失败的情况
								console.error(uploadFileError);
								onFail(uploadFileError)
							},
							complete() {
								uni.hideLoading()
							}
						});
					},
					fail: (err) => {
						onFail(err)
					}
				})
			}
			// 图片文件上传
			else {
				uni.chooseMedia({
					...options,
					mediaType: ['image'],
					success(res) {
						const {
							tempFiles,
							tempFilePaths
						} = res
						console.log("WX:tempFiles", res)
						// 目前做的都是单选情况
						if (size && tempFiles[0].size > size * 1024 * 1024) return uni.showToast({
							title: `文件大小不可超过${size}MB`,
							icon: 'none'
						})
						const actionUrl = `${baseApi}/api/exhibition/v1/commons/upload`;
						uni.showLoading({
							mask: true
						})
						uni.uploadFile({
							url: actionUrl, // 你的上传接口地址
							filePath: tempFiles[0].tempFilePath,
							name: 'file', // 这里根据后端需要的字段来定义
							formData: {
								'file': tempFiles[0], // 其他要传的参数可以在这里添加
								'name': tempFiles[0].fileType === 'image' ? `微信图片.png` : tempFiles[0]
									?.name
							},
							success: uploadFileRes => {
								let res = JSON.parse(uploadFileRes.data)
								console.log(res);
								onSuccess({
									filePath: res?.data,
									fileName: tempFiles[0].fileType === 'image' ?
										`微信图片.png` : tempFiles[0]?.name
								})
								// 处理上传成功后的结果
							},
							fail: uploadFileError => {
								// 处理上传失败的情况
								console.error(uploadFileError);
								onFail(uploadFileError)
							},
							complete() {
								uni.hideLoading()
							}
						});
					}
				})
			}
			// #endif

			// #ifndef MP-WEIXIN
			uni.chooseFile({
				...options,
				complete(res) {
					const {
						tempFiles,
						tempFilePaths
					} = res
					console.log(":tempFiles", res)
					// 单选情况
					if (size && tempFiles[0].size > size * 1024 * 1024) return uni.showToast({
						title: `文件大小不可超过${size}MB`,
						icon: 'none'
					})
					const actionUrl = `${baseApi}/api/exhibition/v1/commons/upload`;
					let formData = new FormData()
					formData.append('file', tempFiles[0])
					uni.showLoading({
						mask: true
					})
					uni.uploadFile({
						url: actionUrl, // 你的上传接口地址
						filePath: tempFilePaths[0],
						name: 'file', // 这里根据后端需要的字段来定义
						formData: {
							'file': formData, // 其他要传的参数可以在这里添加
							'name': tempFiles[0].name
						},
						success: uploadFileRes => {
							let res = JSON.parse(uploadFileRes.data)
							console.log(res);
							onSuccess({
								filePath: res?.data,
								fileName: tempFiles[0].name
							})
							// 处理上传成功后的结果
						},
						fail: uploadFileError => {
							// 处理上传失败的情况
							onFail(uploadFileError)
							console.error(uploadFileError);
						},
						complete() {
							uni.hideLoading()
						}
					});
				}
			})
			// #endif
		},
		goPage(path, type = 'navigateTo') {
			path ?
				uni[type]({
					url: path
				}) : uni.showToast({
					title: '暂无跳转地址',
					icon: 'none'
				})
		},
		handlerCopy(value, type = 'set') {
			if (type === 'set') uni.setClipboardData({
				data: value
			})
			else return uni.getClipboardData()
		},
		handlerActionCall(res, cb) {
			if (res.data.code === 200) {
				cb()
				console.log("lalalalalla", res)
			} else {
				uni.showToast({
					icon: 'none',
					title: res.data.msg
				})
			}
		},
		validatePhoneNumber(phoneNumber, cb) {
			const phoneRegex = /^1[3-9]\d{9}$/;
			if (!phoneRegex.test(phoneNumber)) {
				cb({
					code: 0
				});
			} else cb({
				code: 1
			});
		},
		// 退出
		async exit() {
			let _ = this
			const exitData = await _.exitLogin()
			console.log("exitData", exitData)
			const {
				data
			} = exitData
			if (data.code === 200) {
				uni.clearStorage({
					success: () => {
						uni.navigateTo({
							url: '/pages/login/login'
						})
					}
				})
			} else {
				uni.showToast({
					title: data.msg,
					icon: 'none'
				})
			}
		},
		// 检验登录页拦截
		isLogin() {
			const {
				configLogin
			} = this
			//获取用户的token
			const token = uni.getStorageSync('uniExhibitionToken');
			const nowPage = getCurrentPages();
			//需要登录
			if (!token && !configLogin.whiteList.includes(nowPage)) {
				uni.showToast({
					title: '请先登录',
					icon: 'none',
					duration: 1000,
					success() {
						setTimeout(() => {
							uni.navigateTo({
								url: configLogin.loginPage
							})
						}, 500)
					}
				})
				return false
			}
		}
	}
}