<template>
	<view class="demo-container">
		<!-- 导航栏 -->
		<uni-nav-bar 
			:fixed="true" 
			:shadow="true" 
			:border="false"
			background-color="#007AFF"
			color="#FFFFFF"
			title="示例页面"
			left-icon="left"
			@clickLeft="goBack"
		/>
		
		<!-- 内容区域 -->
		<view class="content">
			<view class="section">
				<view class="section-title">基础组件示例</view>
				
				<!-- 按钮示例 -->
				<view class="demo-item">
					<text class="demo-label">按钮组件：</text>
					<button type="primary" size="mini" @click="handleClick">点击按钮</button>
				</view>
				
				<!-- 输入框示例 -->
				<view class="demo-item">
					<text class="demo-label">输入框：</text>
					<uni-easyinput 
						v-model="inputValue" 
						placeholder="请输入内容"
						:clearable="true"
					/>
				</view>
				
				<!-- 列表示例 -->
				<view class="demo-item">
					<text class="demo-label">列表组件：</text>
					<uni-list>
						<uni-list-item 
							v-for="(item, index) in listData" 
							:key="index"
							:title="item.title"
							:note="item.note"
							:rightText="item.rightText"
							clickable
							@click="handleListClick(item)"
						/>
					</uni-list>
				</view>
			</view>
			
			<!-- API调用示例 -->
			<view class="section">
				<view class="section-title">API调用示例</view>
				<view class="demo-item">
					<button type="default" @click="loadData">加载数据</button>
					<view v-if="loading" class="loading-text">加载中...</view>
					<view v-if="apiData" class="api-result">
						<text>API返回数据：{{ JSON.stringify(apiData) }}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { getDataList } from '@/api/rs.js'

export default {
	name: 'DemoPage',
	data() {
		return {
			inputValue: '',
			loading: false,
			apiData: null,
			listData: [
				{
					title: '列表项1',
					note: '这是列表项1的描述',
					rightText: '详情'
				},
				{
					title: '列表项2', 
					note: '这是列表项2的描述',
					rightText: '详情'
				},
				{
					title: '列表项3',
					note: '这是列表项3的描述', 
					rightText: '详情'
				}
			]
		}
	},
	methods: {
		// 返回上一页
		goBack() {
			uni.navigateBack()
		},
		
		// 按钮点击事件
		handleClick() {
			uni.showToast({
				title: '按钮被点击了！',
				icon: 'success'
			})
		},
		
		// 列表项点击事件
		handleListClick(item) {
			uni.showModal({
				title: '提示',
				content: `您点击了：${item.title}`,
				showCancel: false
			})
		},
		
		// 加载数据示例
		async loadData() {
			this.loading = true
			try {
				// 这里调用API接口
				const result = await getDataList({
					page: 1,
					pageSize: 10
				})
				this.apiData = result
				uni.showToast({
					title: '数据加载成功',
					icon: 'success'
				})
			} catch (error) {
				console.error('加载数据失败：', error)
				uni.showToast({
					title: '加载失败',
					icon: 'error'
				})
			} finally {
				this.loading = false
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.demo-container {
	background-color: #f8f8f8;
	min-height: 100vh;
}

.content {
	padding: 100px 20px 20px;
}

.section {
	background-color: #ffffff;
	border-radius: 10px;
	padding: 20px;
	margin-bottom: 20px;
}

.section-title {
	font-size: 18px;
	font-weight: bold;
	color: #333333;
	margin-bottom: 15px;
}

.demo-item {
	margin-bottom: 15px;
	
	&:last-child {
		margin-bottom: 0;
	}
}

.demo-label {
	font-size: 14px;
	color: #666666;
	margin-bottom: 8px;
	display: block;
}

.loading-text {
	color: #999999;
	font-size: 14px;
	margin-top: 10px;
}

.api-result {
	background-color: #f5f5f5;
	padding: 10px;
	border-radius: 5px;
	margin-top: 10px;
	
	text {
		font-size: 12px;
		color: #333333;
		word-break: break-all;
	}
}
</style>
