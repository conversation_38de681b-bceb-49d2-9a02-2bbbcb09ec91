import App from './App'
import http from './utils/request'
import sjHttp from './utils/requestSj'
import comMixins from '@/mixins/mixins.js'
// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'
Vue.config.productionTip = false
Vue.prototype.$http = http
Vue.prototype.$sjHttp = sjHttp
Vue.mixin(comMixins)
App.mpType = 'app'
const app = new Vue({
  ...App
})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
export function createApp() {
  const app = createSSRApp(App)
  return {
    app
  }
}
// #endif